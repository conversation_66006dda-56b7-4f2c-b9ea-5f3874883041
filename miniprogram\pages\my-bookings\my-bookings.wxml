<!--my-bookings.wxml-->
<view class="container">


  <!-- 预约状态筛选栏 - 使用TDesign t-tabs组件 -->
  <view class="view-section">
    <!--
      顶部选项卡区域容器
      使用与schedule页面相同的样式设计
    -->
    <view class="top-tabs-section">
      <!--
        TDesign线条选项卡组件

        属性说明：
        - value: 当前激活的选项卡值，对应activeTab数据
        - bindchange: 选项卡切换事件，调用onTabChange方法
        - theme: 选项卡主题，"line"表示线条式选项卡
        - show-bottom-line: 是否显示底部分割线
        - t-class: 自定义样式类名，使用custom-top-tabs样式

        与原来的booking-tabs对比：
        - 原来：自定义div + CSS实现的简单tab
        - 现在：TDesign专业组件，样式更美观，交互更流畅

        与schedule页面保持一致的设计：
        - 相同的组件和样式
        - 相同的交互体验
        - 统一的视觉风格
      -->
      <t-tabs value="{{activeTab}}" bindchange="onTabChange" theme="line" show-bottom-line="{{true}}" t-class="custom-top-tabs">
        <!--
          选项卡面板：定义具体的tab项
          t-tab-panel: TDesign的选项卡面板组件

          使用wx:for循环渲染bookingTabs数组中的每个选项
          这样可以保持数据驱动的设计模式
        -->
        <t-tab-panel
          wx:for="{{bookingTabs}}"
          wx:key="value"
          value="{{item.value}}"
          label="{{item.label}}"
        />
      </t-tabs>
    </view>
  </view>

  <!-- 全部活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'all'}}">
    <t-empty wx:if="{{filteredAllBookings.length === 0}}" description="暂无活动记录" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onAllScrollToLower"
      bindscrolltoupper="onAllScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggered}}"
      bindrefresherrefresh="onRefresherRefresh"
    >
      <view wx:if="{{isLoadingTop}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistory}}" class="end-indicator">到顶啦！</view>
      <block wx:for="{{visibleAllBookings}}" wx:key="id">
        <!-- 时间轴分组：如果是新日期，显示日期分隔 -->
        <view wx:if="{{index === 0 || visibleAllBookings[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexes.indexOf(index) !== -1 ? ' slide-in' : ''}}"
          bind:tap="viewCourseDetail"
          data-course="{{item}}"
        >
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item" wx:if="{{item.venue}}">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
          </view>
          <view class="booking-actions">
            <t-button 
              wx:if="{{item.canCancel}}"
              size="small" 
              theme="default"
              variant="outline"
              bind:tap="cancelBooking"
              data-booking="{{item}}"
            >
              取消预约
            </t-button>
          </view>
        </view>
      </block>
      <view wx:if="{{isLoadingBottom}}" class="loading-indicator">加载未来中...</view>
      <view wx:elif="{{noMoreFuture}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>

  <!-- 未上活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'upcoming'}}">
    <t-empty wx:if="{{filteredUpcomingBookings.length === 0}}" description="暂无已预约活动" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;">
      <view 
        class="booking-card" 
        wx:for="{{filteredUpcomingBookings}}" 
        wx:key="id"
        bind:tap="viewCourseDetail"
        data-course="{{item}}"
      >
        <view class="booking-status {{item.status}}">{{item.statusText}}</view>
        <view class="course-title">{{item.courseName}}</view>
        <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
          <view></view>
        </view>
        <view class="course-info-list">
          <view class="info-item">
            <t-icon name="time" size="16" />
            <text>{{item.date}} {{item.time}}</text>
          </view>
          <view class="info-item">
            <t-icon name="user" size="16" />
            <text>{{item.coach}}</text>
          </view>
          <view class="info-item">
            <t-icon name="location" size="16" />
            <text>{{item.venue}}</text>
          </view>
        </view>
        <view class="booking-actions">
          <t-button 
            wx:if="{{item.canCancel}}"
            size="small" 
            theme="default"
            variant="outline"
            bind:tap="cancelBooking"
            data-booking="{{item}}"
          >
            取消预约
          </t-button>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 已取消活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'cancelled'}}">
    <t-empty wx:if="{{filteredCancelledBookings.length === 0}}" description="暂无已取消活动" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onCancelledScrollToLower"
      bindscrolltoupper="onCancelledScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggeredCancelled}}"
      bindrefresherrefresh="onRefresherRefreshCancelled"
    >
      <view wx:if="{{isLoadingTopCancelled}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistoryCancelled}}" class="end-indicator">到顶啦！</view>
      <block wx:for="{{visibleCancelledBookings}}" wx:key="id">
        <view wx:if="{{index === 0 || visibleCancelledBookings[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexesCancelled.indexOf(index) !== -1 ? ' slide-in' : ''}}"
          bind:tap="viewCourseDetail"
          data-course="{{item}}"
        >
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item" wx:if="{{item.venue}}">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
          </view>
          <!-- booking-actions 不再显示按钮 -->
        </view>
      </block>
      <view wx:if="{{isLoadingBottomCancelled}}" class="loading-indicator">加载未来中...</view>
      <view wx:elif="{{noMoreFutureCancelled}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>

  <!-- 已完成活动列表 -->
  <view class="booking-list" wx:if="{{activeTab === 'completed'}}">
    <t-empty wx:if="{{filteredCompletedBookings.length === 0}}" description="暂无已完成活动" />
    <scroll-view wx:else scroll-y="true" style="flex:1;min-height:0;"
      bindscrolltolower="onCompletedScrollToLower"
      bindscrolltoupper="onCompletedScrollToUpper"
      refresher-enabled="true"
      refresher-triggered="{{isRefresherTriggeredCompleted}}"
      bindrefresherrefresh="onRefresherRefreshCompleted"
    >
      <view wx:if="{{isLoadingTopCompleted}}" class="loading-indicator">加载历史中...</view>
      <view wx:elif="{{noMoreHistoryCompleted}}" class="end-indicator">到顶啦！</view>
      <block wx:for="{{visibleCompletedBookings}}" wx:key="id">
        <view wx:if="{{index === 0 || visibleCompletedBookings[index-1].date !== item.date}}" class="timeline-date">{{item.date}}</view>
        <view
          class="booking-card{{flashIndexesCompleted.indexOf(index) !== -1 ? ' slide-in' : ''}}"
        >
          <view class="booking-status {{item.status}}">{{item.statusText}}</view>
          <view class="course-title">{{item.courseName}}</view>
          <view class="course-header" style="display:flex;justify-content:space-between;align-items:center;">
            <view></view>
          </view>
          <view class="course-info-list">
            <view class="info-item">
              <t-icon name="time" size="16" />
              <text>{{item.date}} {{item.time}}</text>
            </view>
            <view class="info-item">
              <t-icon name="user" size="16" />
              <text>{{item.coach}}</text>
            </view>
            <view class="info-item" wx:if="{{item.venue}}">
              <t-icon name="location" size="16" />
              <text>{{item.venue}}</text>
            </view>
          </view>
        </view>
      </block>
      <view wx:if="{{isLoadingBottomCompleted}}" class="loading-indicator">加载未来中...</view>
      <view wx:elif="{{noMoreFutureCompleted}}" class="end-indicator">到底啦！</view>
    </scroll-view>
  </view>
  <t-toast id="t-toast" />
</view>